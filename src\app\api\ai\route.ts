import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { obtenerRespuestaIA } from '@/lib/gemini/questionService';
import { ApiAIInputSchema } from '@/lib/zodSchemas';
// Los tokens se manejan automáticamente por tokenTracker.ts en las funciones de IA
import { PlanValidationService } from '@/lib/services/planValidation';
import { FreeAccountService } from '@/lib/services/freeAccountService';
import { DocumentChunkingService } from '@/lib/services/DocumentChunkingService';
import { type Documento } from '@/types/database';
import * as Sentry from "@sentry/nextjs";
import { TokenEstimationService } from '@/lib/services/tokenEstimationService';
import { getFeatureTokensRequired, FEATURE_IDS } from '@/config/features';

/**
 * Obtiene la URL base correcta para llamadas internas en Vercel
 */
function getInternalBaseUrl(): string {
  // En Vercel, usar la variable de entorno del sistema para llamadas internas
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  // Fallback para desarrollo local
  return process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
}

/**
 * Dispara el procesamiento inmediato de una tarea AI
 */
function triggerImmediateProcessing(taskId: string): void {
  const baseUrl = getInternalBaseUrl();

  fetch(`${baseUrl}/api/tasks/process-ai-job`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      // Incluir autorización para el worker protegido
      'Authorization': `Bearer ${process.env.CRON_SECRET}`
    },
    body: JSON.stringify({ taskId })
  }).catch(error => {
    console.warn('⚠️ Procesamiento inmediato falló, el cron job lo retomará:', error.message);
  });
}

/**
 * Detecta si se usará chunking para los contextos dados
 * Replica la lógica de prepararDocumentos para determinar chunking
 */
function willUseChunking(contextos: string[]): boolean {
  // Solo se usa chunking para un solo documento
  if (contextos.length !== 1) {
    return false;
  }

  const contenido = contextos[0];
  if (!contenido) {
    return false;
  }

  // Usar la misma lógica que DocumentChunkingService para determinar chunking
  const threshold = 15000; // MIN_SIZE_FOR_CHUNKING del config
  return contenido.length > threshold;
}
import { ERROR_MESSAGES } from '@/config/constants';
import { SupabaseAdminService } from '@/lib/supabase/admin';

/**
 * Obtiene el contenido de documentos desde Storage o fallback a contenido en BD
 * @param documentos Array de documentos que pueden tener storage_path o contenido
 * @returns Array de strings con el contenido de los documentos
 */
async function obtenerContenidoDocumentos(documentos: any[]): Promise<string[]> {
  const { downloadFileContentFromServer } = await import('@/lib/supabase/storageService.server');

  const contenidos = await Promise.all(
    documentos.map(async (doc) => {
      try {
        // Prioridad 1: Descargar desde Storage si existe storage_path
        if (doc.storage_path) {
          const contenidoStorage = await downloadFileContentFromServer(doc.storage_path);
          if (contenidoStorage) {
            // Verificar si el contenido es texto legible o binario corrupto
            const isTextContent = /^[\x20-\x7E\s\u00A0-\uFFFF]*$/.test(contenidoStorage.substring(0, 100));
            if (isTextContent && contenidoStorage.length > 100) {
              return contenidoStorage;
            } else {
              console.warn('⚠️ [AI] Contenido de Storage parece ser binario (PDF), usando fallback a BD');
            }
          } else {
            console.warn('⚠️ [AI] No se pudo descargar desde Storage, usando fallback a BD');
          }
        }

        // Prioridad 2: Usar contenido_corto si existe (enfoque híbrido)
        if (doc.contenido_corto) {
          return doc.contenido_corto;
        }

        // Prioridad 3: Usar contenido tradicional si existe (compatibilidad)
        if (doc.contenido) {
          return doc.contenido;
        }

        // Si llegamos aquí, el documento no tiene ninguna fuente de contenido.
        console.error('❌ [AI] Documento sin contenido disponible:', doc.id || 'ID desconocido');
        throw new Error(`El documento "${doc.titulo || 'Sin título'}" no tiene contenido disponible para ser procesado.`);

      } catch (error) {
        console.error('❌ [AI] Error al obtener contenido del documento:', {
          docId: doc.id || 'ID desconocido',
          titulo: doc.titulo || 'Sin título',
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
        // Relanzar el error para que el endpoint principal lo capture
        throw error;
      }
    })
  );

  return contenidos;
}

// Helper function to increment usage count for free accounts
// NOTA: Los tokens se incrementan automáticamente por tokenTracker.ts
// Este método solo incrementa contadores de features (tests, flashcards, mindMaps)
async function incrementUsageForFreeAccount(
  userId: string,
  feature: 'tests' | 'flashcards' | 'mindMaps',
  amount: number = 1
): Promise<void> {
  try {
    // Get user profile to check plan
    const profile = await SupabaseAdminService.getUserProfile(userId);

    if (profile?.subscription_plan === 'free') {
      const success = await FreeAccountService.incrementUsageCount(userId, feature, amount);
      if (!success) {
        console.warn(`⚠️ Failed to increment ${feature} usage count for user ${userId}`);
      } else {
      }
    }
  } catch (error) {
    console.error(`❌ Error incrementing usage for user ${userId}:`, error);
  }
}

// API route for AI actions (formerly Gemini)
export async function POST(req: NextRequest) {
  let user = null;
  let body = null;



  try {

    // Crear cliente de Supabase usando la implementación correcta
    const supabase = await createServerSupabaseClient();
    const { data: { user: authUser }, error: userError } = await supabase.auth.getUser();
    user = authUser;

    if (!user) {
      return NextResponse.json({
        error: ERROR_MESSAGES.UNAUTHORIZED,
        debug: {
          userError: userError?.message,
          hasCookies: !!req.headers.get('cookie')
        }
      }, { status: 401 });
    }

    body = await req.json();

    // Validación robusta de entrada
    const parseResult = ApiAIInputSchema.safeParse(body);
    if (!parseResult.success) {
      return NextResponse.json({
        error: ERROR_MESSAGES.INVALID_DATA,
        detalles: parseResult.error.errors
      }, { status: 400 });
    }


    // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA
    if (body.pregunta && body.documentos) {
      // Obtener contenido de documentos desde Storage o fallback
      const documentosContent = await obtenerContenidoDocumentos(body.documentos);

      const chatEstimation = TokenEstimationService.estimateForChat(documentosContent);

      // Usar estimación dinámica, con fallback a valor fijo si hay problemas
      const chatRequiredTokens = chatEstimation.isWithinLimits
        ? chatEstimation.totalEstimated
        : getFeatureTokensRequired(FEATURE_IDS.AI_TUTOR_CHAT);

      // Validar acceso al chat con IA para usuarios gratuitos
      const chatValidation = await PlanValidationService.validateFeatureAccess(
        user.id,
        'ai_tutor_chat',
        chatRequiredTokens
      );

      if (!chatValidation.allowed) {
        return NextResponse.json({
          error: 'Acceso denegado: ' + chatValidation.reason
        }, { status: 403 });
      }

      // Crear documentos con contenido descargado para obtenerRespuestaIA
      const documentosConContenido = body.documentos.map((doc: any, index: number) => {
        const contenido = documentosContent[index];
        if (!contenido || contenido.trim() === '') {
          throw new Error(`El documento "${doc.titulo || 'Sin título'}" no tiene contenido disponible para el chat.`);
        }
        return {
          titulo: doc.titulo || 'Documento sin título',
          contenido: contenido,
          categoria: doc.categoria,
          numero_tema: doc.numero_tema
        };
      });

      const result = await obtenerRespuestaIA(body.pregunta, documentosConContenido);

      // ✅ CORREGIDO: No hacer tracking aquí porque ya se hace en llamarOpenAI()
      // El tracking se realiza automáticamente en openaiClient.ts con datos reales

      return NextResponse.json({ result });
    }

    const { action, peticion, contextos, documentos, cantidad, temarioId } = body;


    // Forzar recompilación

    let result;

    switch (action) {
      case 'generarTest':
        // Obtener contextos desde documentos si se proporcionaron, sino usar contextos directos
        let contextosParaTest = contextos;
        let documentosConContenido = null;

        if (documentos && documentos.length > 0) {
          const contenidos = await obtenerContenidoDocumentos(documentos);

          // *** INICIO DE LA CORRECCIÓN ***
          // Enriquecer los documentos originales con el contenido descargado
          documentosConContenido = documentos.map((doc: any, index: number) => ({
            ...doc,
            contenido: contenidos[index] || ''
          }));
          contextosParaTest = contenidos;
          // *** FIN DE LA CORRECCIÓN ***
        }

        // Detectar si se usará chunking para usar la estimación correcta
        const willUseChunkingForTests = willUseChunking(contextosParaTest);

        let testEstimation;
        if (willUseChunkingForTests) {
          // Si se usará chunking, crear chunks temporales para estimación precisa
          const documentoTemporal: Documento = {
            id: `temp_${Date.now()}`,
            titulo: 'Documento temporal',
            contenido: contextosParaTest[0],
            categoria: 'General',
            numero_tema: 1,
            creado_en: new Date().toISOString(),
            actualizado_en: new Date().toISOString(),
            user_id: 'temp_user'
          };

          const resultado = DocumentChunkingService.processDocument(documentoTemporal, {
            enableChunking: true,
            contentType: 'default'
          });

          if (resultado.wasChunked && Array.isArray(resultado.content)) {
            testEstimation = TokenEstimationService.estimateForTestsWithChunks(resultado.content, cantidad || 10);
          } else {
            testEstimation = TokenEstimationService.estimateForTests(contextosParaTest, cantidad || 10);
          }
        } else {
          // Usar estimación tradicional para contextos múltiples o documentos pequeños
          testEstimation = TokenEstimationService.estimateForTests(contextosParaTest, cantidad || 10);
        }

        // Usar estimación dinámica, con fallback a valor fijo si hay problemas
        const testRequiredTokens = testEstimation.isWithinLimits
          ? testEstimation.totalEstimated
          : getFeatureTokensRequired(FEATURE_IDS.TEST_GENERATION);

        // Validar acceso a generación de tests
        const testValidation = await PlanValidationService.validateFeatureAccess(
          user.id,
          'test_generation',
          testRequiredTokens
        );

        if (!testValidation.allowed) {
          return NextResponse.json({
            error: 'Acceso denegado: ' + testValidation.reason
          }, { status: 403 });
        }

        // *** INICIO DE LA NUEVA LÓGICA ASÍNCRONA ***
        // 1. Verificar si ya hay una tarea de test en proceso
        const { data: existingTestTasks, error: checkError } = await supabase
          .from('ai_tasks')
          .select('id, status')
          .eq('user_id', user.id)
          .eq('type', 'test')
          .in('status', ['pending', 'processing']);

        if (checkError) {
          return NextResponse.json({ error: 'Error al verificar tareas existentes' }, { status: 500 });
        }

        if (existingTestTasks && existingTestTasks.length > 0) {
          return NextResponse.json({
            error: 'Ya tienes una generación de test en proceso. Espera a que termine antes de crear otra.'
          }, { status: 409 });
        }

        // 2. Crear la tarea en la base de datos
        const { data: newTestTask, error: testTaskError } = await supabase
          .from('ai_tasks')
          .insert({
            user_id: user.id,
            type: 'test',
            input_data: {
              peticion,
              documentos: documentosConContenido || contextosParaTest,
              cantidad
            }
          })
          .select()
          .single();

        if (testTaskError) {
          return NextResponse.json({ error: 'Error al crear la tarea de test' }, { status: 500 });
        }

        // 2. Disparar procesamiento inmediato (sin esperar respuesta)
        triggerImmediateProcessing(newTestTask.id);

        // 3. Devolver el ID de la tarea al frontend inmediatamente
        result = { taskId: newTestTask.id, status: 'pending' };

        // 4. Incrementar contador de uso para cuentas gratuitas
        await incrementUsageForFreeAccount(user.id, 'tests', cantidad || 1);
        // *** FIN DE LA NUEVA LÓGICA ASÍNCRONA ***
        break;
      case 'generarFlashcards':
        // Obtener contextos desde documentos si se proporcionaron, sino usar contextos directos
        let contextosParaFlashcards = contextos;
        let documentosConContenidoFlashcards = null;

        if (documentos && documentos.length > 0) {
          const contenidos = await obtenerContenidoDocumentos(documentos);

          // *** INICIO DE LA CORRECCIÓN ***
          // Enriquecer los documentos originales con el contenido descargado
          documentosConContenidoFlashcards = documentos.map((doc: any, index: number) => ({
            ...doc,
            contenido: contenidos[index] || ''
          }));
          contextosParaFlashcards = contenidos;
          // *** FIN DE LA CORRECCIÓN ***
        }

        // Detectar si se usará chunking para usar la estimación correcta
        const willUseChunkingForFlashcards = willUseChunking(contextosParaFlashcards);

        let flashcardEstimation;
        if (willUseChunkingForFlashcards) {
          // Si se usará chunking, crear chunks temporales para estimación precisa
          const documentoTemporal: Documento = {
            id: `temp_${Date.now()}`,
            titulo: 'Documento temporal',
            contenido: contextosParaFlashcards[0],
            categoria: 'General',
            numero_tema: 1,
            creado_en: new Date().toISOString(),
            actualizado_en: new Date().toISOString(),
            user_id: 'temp_user'
          };

          const resultado = DocumentChunkingService.processDocument(documentoTemporal, {
            enableChunking: true,
            contentType: 'default'
          });

          if (resultado.wasChunked && Array.isArray(resultado.content)) {
            flashcardEstimation = TokenEstimationService.estimateForFlashcardsWithChunks(resultado.content, cantidad || 10);
          } else {
            flashcardEstimation = TokenEstimationService.estimateForFlashcards(contextosParaFlashcards, cantidad || 10);
          }
        } else {
          // Usar estimación tradicional para contextos múltiples o documentos pequeños
          flashcardEstimation = TokenEstimationService.estimateForFlashcards(contextosParaFlashcards, cantidad || 10);
        }

        // Usar estimación dinámica, con fallback a valor fijo si hay problemas
        const flashcardRequiredTokens = flashcardEstimation.isWithinLimits
          ? flashcardEstimation.totalEstimated
          : getFeatureTokensRequired(FEATURE_IDS.FLASHCARD_GENERATION);

        // Validar acceso a generación de flashcards
        const flashcardValidation = await PlanValidationService.validateFeatureAccess(
          user.id,
          'flashcard_generation',
          flashcardRequiredTokens
        );

        if (!flashcardValidation.allowed) {
          return NextResponse.json({
            error: 'Acceso denegado: ' + flashcardValidation.reason
          }, { status: 403 });
        }

        // *** INICIO DE LA NUEVA LÓGICA ASÍNCRONA ***
        // 1. Verificar si ya hay una tarea de flashcards en proceso
        const { data: existingFlashcardTasks, error: checkFlashcardError } = await supabase
          .from('ai_tasks')
          .select('id, status')
          .eq('user_id', user.id)
          .eq('type', 'flashcards')
          .in('status', ['pending', 'processing']);

        if (checkFlashcardError) {
          return NextResponse.json({ error: 'Error al verificar tareas existentes' }, { status: 500 });
        }

        if (existingFlashcardTasks && existingFlashcardTasks.length > 0) {
          return NextResponse.json({
            error: 'Ya tienes una generación de flashcards en proceso. Espera a que termine antes de crear otra.'
          }, { status: 409 });
        }

        // 2. Crear la tarea en la base de datos
        const { data: newFlashcardTask, error: flashcardTaskError } = await supabase
          .from('ai_tasks')
          .insert({
            user_id: user.id,
            type: 'flashcards',
            input_data: {
              peticion,
              documentos: documentosConContenidoFlashcards || contextosParaFlashcards,
              cantidad
            }
          })
          .select()
          .single();

        if (flashcardTaskError) {
          return NextResponse.json({ error: 'Error al crear la tarea de flashcards' }, { status: 500 });
        }

        // 2. Disparar procesamiento inmediato (sin esperar respuesta)
        triggerImmediateProcessing(newFlashcardTask.id);

        // 3. Devolver el ID de la tarea al frontend inmediatamente
        result = { taskId: newFlashcardTask.id, status: 'pending' };

        // 4. Incrementar contador de uso para cuentas gratuitas
        await incrementUsageForFreeAccount(user.id, 'flashcards', cantidad || 1);
        // *** FIN DE LA NUEVA LÓGICA ASÍNCRONA ***
        break;
      case 'generarMapaMental':
        // Obtener contextos desde documentos si se proporcionaron, sino usar contextos directos
        let contextosParaMapaMental = contextos;
        let documentosConContenidoMapaMental = null;

        if (documentos && documentos.length > 0) {
          const contenidos = await obtenerContenidoDocumentos(documentos);

          // *** INICIO DE LA CORRECCIÓN ***
          // Enriquecer los documentos originales con el contenido descargado
          documentosConContenidoMapaMental = documentos.map((doc: any, index: number) => ({
            ...doc,
            contenido: contenidos[index] || ''
          }));
          contextosParaMapaMental = contenidos;
          // *** FIN DE LA CORRECCIÓN ***
        }

        // Detectar si se usará chunking para usar la estimación correcta
        const willUseChunkingForMindMaps = willUseChunking(contextosParaMapaMental);

        let mindMapEstimation;
        if (willUseChunkingForMindMaps) {
          // Si se usará chunking, crear chunks temporales para estimación precisa
          const documentoTemporal: Documento = {
            id: `temp_${Date.now()}`,
            titulo: 'Documento temporal',
            contenido: contextosParaMapaMental[0],
            categoria: 'General',
            numero_tema: 1,
            creado_en: new Date().toISOString(),
            actualizado_en: new Date().toISOString(),
            user_id: 'temp_user'
          };

          const resultado = DocumentChunkingService.processDocument(documentoTemporal, {
            enableChunking: true,
            contentType: 'default'
          });

          if (resultado.wasChunked && Array.isArray(resultado.content)) {
            mindMapEstimation = TokenEstimationService.estimateForMindMapsWithChunks(resultado.content);
          } else {
            mindMapEstimation = TokenEstimationService.estimateForMindMaps(contextosParaMapaMental);
          }
        } else {
          // Usar estimación tradicional para contextos múltiples o documentos pequeños
          mindMapEstimation = TokenEstimationService.estimateForMindMaps(contextosParaMapaMental);
        }

        // Usar estimación dinámica, con fallback a valor fijo si hay problemas
        const mindMapRequiredTokens = mindMapEstimation.isWithinLimits
          ? mindMapEstimation.totalEstimated
          : getFeatureTokensRequired(FEATURE_IDS.MIND_MAP_GENERATION);

        // Validar acceso a generación de mapas mentales
        const mindMapValidation = await PlanValidationService.validateFeatureAccess(
          user.id,
          'mind_map_generation',
          mindMapRequiredTokens
        );

        if (!mindMapValidation.allowed) {
          return NextResponse.json({
            error: 'Acceso denegado: ' + mindMapValidation.reason
          }, { status: 403 });
        }

        // *** INICIO DE LA NUEVA LÓGICA ASÍNCRONA ***
        // 1. Verificar si ya hay una tarea de mapa mental en proceso
        const { data: existingMindMapTasks, error: checkMindMapError } = await supabase
          .from('ai_tasks')
          .select('id, status')
          .eq('user_id', user.id)
          .eq('type', 'mindmap')
          .in('status', ['pending', 'processing']);

        if (checkMindMapError) {
          return NextResponse.json({ error: 'Error al verificar tareas existentes' }, { status: 500 });
        }

        if (existingMindMapTasks && existingMindMapTasks.length > 0) {
          return NextResponse.json({
            error: 'Ya tienes una generación de mapa mental en proceso. Espera a que termine antes de crear otro.'
          }, { status: 409 });
        }

        // 2. Crear la tarea en la base de datos
        const { data: newMindMapTask, error: mindMapTaskError } = await supabase
          .from('ai_tasks')
          .insert({
            user_id: user.id,
            type: 'mindmap',
            input_data: {
              peticion,
              documentos: documentosConContenidoMapaMental || contextosParaMapaMental
            }
          })
          .select()
          .single();

        if (mindMapTaskError) {
          return NextResponse.json({ error: 'Error al crear la tarea de mapa mental' }, { status: 500 });
        }

        // 2. Disparar procesamiento inmediato (sin esperar respuesta)
        triggerImmediateProcessing(newMindMapTask.id);

        // 3. Devolver el ID de la tarea al frontend inmediatamente
        result = { taskId: newMindMapTask.id, status: 'pending' };

        // 4. Incrementar contador de uso para cuentas gratuitas
        await incrementUsageForFreeAccount(user.id, 'mindMaps', 1);
        // *** FIN DE LA NUEVA LÓGICA ASÍNCRONA ***
        break;
      case 'generarResumen':
        // Obtener contextos desde documentos si se proporcionaron, sino usar contextos directos
        let contextosParaResumen = contextos;
        if (documentos && documentos.length > 0) {
          
          contextosParaResumen = await obtenerContenidoDocumentos(documentos);
        }

        // Detectar si se usará chunking para usar la estimación correcta
        const willUseChunkingForSummaries = willUseChunking(contextosParaResumen);

        let summaryEstimation;
        if (willUseChunkingForSummaries) {
          // Si se usará chunking, crear chunks temporales para estimación precisa
          const documentoTemporal: Documento = {
            id: `temp_${Date.now()}`,
            titulo: 'Documento temporal',
            contenido: contextosParaResumen[0],
            categoria: 'General',
            numero_tema: 1,
            creado_en: new Date().toISOString(),
            actualizado_en: new Date().toISOString(),
            user_id: 'temp_user'
          };

          const resultado = DocumentChunkingService.processDocument(documentoTemporal, {
            enableChunking: true,
            contentType: 'default'
          });

          if (resultado.wasChunked && Array.isArray(resultado.content)) {
            summaryEstimation = TokenEstimationService.estimateForSummariesWithChunks(resultado.content);
          } else {
            summaryEstimation = TokenEstimationService.estimateForSummaries(contextosParaResumen);
          }
        } else {
          // Usar estimación tradicional para contextos múltiples o documentos pequeños
          summaryEstimation = TokenEstimationService.estimateForSummaries(contextosParaResumen);
        }

        // Usar estimación dinámica, con fallback a valor fijo si hay problemas
        const summaryRequiredTokens = summaryEstimation.isWithinLimits
          ? summaryEstimation.totalEstimated
          : getFeatureTokensRequired(FEATURE_IDS.SUMMARY_A1_A2);

        // Validar acceso a generación de resúmenes
        const summaryValidation = await PlanValidationService.validateFeatureAccess(
          user.id,
          'summary_a1_a2',
          summaryRequiredTokens
        );

        if (!summaryValidation.allowed) {
          return NextResponse.json({
            error: 'Acceso denegado: ' + summaryValidation.reason
          }, { status: 403 });
        }

        // Para resúmenes, esperamos que contextosParaResumen[0] sea el contenido del documento
        if (!contextosParaResumen || contextosParaResumen.length !== 1) {
          throw new Error('Se requiere exactamente un documento para generar un resumen');
        }

        // Crear objeto documento a partir del contexto
        const documento = {
          titulo: peticion.split('|')[0] || 'Documento sin título',
          contenido: contextosParaResumen[0],
          categoria: peticion.split('|')[1],
          numero_tema: peticion.split('|')[2] ? parseInt(peticion.split('|')[2]) : undefined
        };

        const instrucciones = peticion.split('|')[3] || undefined;

        // *** INICIO DE LA NUEVA LÓGICA ASÍNCRONA ***
        // 1. Verificar si ya hay una tarea de resumen en proceso
        const { data: existingSummaryTasks, error: checkSummaryError } = await supabase
          .from('ai_tasks')
          .select('id, status')
          .eq('user_id', user.id)
          .eq('type', 'summary')
          .in('status', ['pending', 'processing']);

        if (checkSummaryError) {
          return NextResponse.json({ error: 'Error al verificar tareas existentes' }, { status: 500 });
        }

        if (existingSummaryTasks && existingSummaryTasks.length > 0) {
          return NextResponse.json({
            error: 'Ya tienes una generación de resumen en proceso. Espera a que termine antes de crear otro.'
          }, { status: 409 });
        }

        // 2. Crear la tarea en la base de datos
        const { data: newSummaryTask, error: summaryTaskError } = await supabase
          .from('ai_tasks')
          .insert({
            user_id: user.id,
            type: 'summary',
            input_data: {
              documento,
              instrucciones
            }
          })
          .select()
          .single();

        if (summaryTaskError) {
          return NextResponse.json({ error: 'Error al crear la tarea de resumen' }, { status: 500 });
        }

        // 2. Disparar procesamiento inmediato (sin esperar respuesta)
        triggerImmediateProcessing(newSummaryTask.id);

        // 3. Devolver el ID de la tarea al frontend inmediatamente
        result = { taskId: newSummaryTask.id, status: 'pending' };
        // *** FIN DE LA NUEVA LÓGICA ASÍNCRONA ***
        break;
      case 'generarPlanEstudios':
        // Para planificación, usar estimación fija (entrada controlada por el sistema)
        const planEstimation = TokenEstimationService.estimateForStudyPlanning();
        const requiredTokens = planEstimation.totalEstimated;

        const planValidation = await PlanValidationService.validateFeatureAccess(
          user.id,
          'study_planning',
          requiredTokens
        );

        if (!planValidation.allowed) {
          return NextResponse.json({
            error: 'Acceso denegado: ' + planValidation.reason
          }, { status: 403 });
        }

        // Para planes de estudios, el temarioId viene en peticion
        const temarioIdFromPeticion = peticion || temarioId;
        if (!temarioIdFromPeticion) {
          throw new Error('Se requiere temarioId para generar el plan de estudios');
        }

        // *** INICIO DE LA NUEVA LÓGICA ASÍNCRONA ***
        // 1. Verificar si ya hay una tarea de plan de estudios en proceso
        const { data: existingPlanTasks, error: checkPlanError } = await supabase
          .from('ai_tasks')
          .select('id, status')
          .eq('user_id', user.id)
          .eq('type', 'plan')
          .in('status', ['pending', 'processing']);

        if (checkPlanError) {
          return NextResponse.json({ error: 'Error al verificar tareas existentes' }, { status: 500 });
        }

        if (existingPlanTasks && existingPlanTasks.length > 0) {
          return NextResponse.json({
            error: 'Ya tienes una generación de plan de estudios en proceso. Espera a que termine antes de crear otro.'
          }, { status: 409 });
        }

        // 2. Crear la tarea en la base de datos
        const { data: newPlanTask, error: planTaskError } = await supabase
          .from('ai_tasks')
          .insert({
            user_id: user.id,
            type: 'plan',
            input_data: {
              temarioId: temarioIdFromPeticion
            }
          })
          .select()
          .single();

        if (planTaskError) {
          return NextResponse.json({ error: 'Error al crear la tarea de plan de estudios' }, { status: 500 });
        }

        // 2. Disparar procesamiento inmediato (sin esperar respuesta)
        triggerImmediateProcessing(newPlanTask.id);

        // 3. Devolver el ID de la tarea al frontend inmediatamente
        result = { taskId: newPlanTask.id, status: 'pending' };
        // *** FIN DE LA NUEVA LÓGICA ASÍNCRONA ***
        break;
      default:
        return NextResponse.json({ error: 'Acción no soportada' }, { status: 400 });
    }

    return NextResponse.json({ result });

  } catch (error: any) {
    // ---> INICIO DEL CÓDIGO A AÑADIR <---
    Sentry.captureException(error, {
      tags: { section: "ai-api" },
      extra: {
        context: "Error in AI API endpoint.",
        timestamp: new Date().toISOString()
      },
    });
    // ---> FIN DEL CÓDIGO A AÑADIR <---

    return NextResponse.json({
      error: 'Error interno del servidor',
      detalles: error.message
    }, { status: 500 });
  }
}
