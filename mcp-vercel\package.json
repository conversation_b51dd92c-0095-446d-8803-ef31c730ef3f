{"name": "vercel", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "node --loader ts-node/esm ./src/index.ts", "build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "start": "node build/index.js"}, "files": ["build"], "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.4", "dotenv": "^16.5.0", "node-fetch": "^3.3.2", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.10.2", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}